/* dnsmasq is Copyright (c) 2000-2024 <PERSON>

   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License as published by
   the Free Software Foundation; version 2 dated June, 1991, or
   (at your option) version 3 dated 29 June, 2007.
 
   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANT<PERSON>ILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License for more details.
     
   You should have received a copy of the GNU General Public License
   along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

#include "dnsmasq.h"
/* CRADLEPOINT */
#include "opendns.h"
/* CRADLEPOINT */

unsigned char *process_ar(unsigned char *ansp, int arcount, struct dns_header *header, size_t plen);
static void insert_appid_catid_to_ipset(unsigned char *ansp, size_t plen);
static void add_appid_catid_to_ipset(struct ipsets **ipset, char *domain, char *set);
static struct ipsets *search_domain_in_ipset(struct ipsets *ipset, char *domain);
static struct ipsets *add_domain_to_ipset(struct ipsets *ipset, char *domain);
static void add_set_to_ipset(struct ipsets *ipset, char *set);
static size_t add_gateway_lan(struct dns_header *header, size_t plen, unsigned char *limit,
                             union mysockaddr *source, int *cacheable);

unsigned char *find_pseudoheader(struct dns_header *header, size_t plen, size_t  *len, unsigned char **p, int *is_sign, int *is_last)
{
  /* See if packet has an RFC2671 pseudoheader, and if so return a pointer to it. 
     also return length of pseudoheader in *len and pointer to the UDP size in *p
     Finally, check to see if a packet is signed. If it is we cannot change a single bit before
     forwarding. We look for TSIG in the addition section, and TKEY queries (for GSS-TSIG) */
  
  int i, arcount = ntohs(header->arcount);
  unsigned char *ansp = (unsigned char *)(header+1);
  unsigned short rdlen, type, class;
  unsigned char *ret = NULL;

  if (is_sign)
    {
      *is_sign = 0;

      if (OPCODE(header) == QUERY)
	{
	  for (i = ntohs(header->qdcount); i != 0; i--)
	    {
	      if (!(ansp = skip_name(ansp, header, plen, 4)))
		return NULL;
	      
	      GETSHORT(type, ansp); 
	      GETSHORT(class, ansp);
	      
	      if (class == C_IN && type == T_TKEY)
		*is_sign = 1;
	    }
	}
    }
  else
    {
      if (!(ansp = skip_questions(header, plen)))
	return NULL;
    }
    
  if (arcount == 0)
    return NULL;
  
  if (!(ansp = skip_section(ansp, ntohs(header->ancount) + ntohs(header->nscount), header, plen)))
    return NULL; 
  
  for (i = 0; i < arcount; i++)
    {
      unsigned char *save, *start = ansp;
      if (!(ansp = skip_name(ansp, header, plen, 10)))
	return NULL; 

      GETSHORT(type, ansp);
      save = ansp;
      GETSHORT(class, ansp);
      ansp += 4; /* TTL */
      GETSHORT(rdlen, ansp);
      if (!ADD_RDLEN(header, ansp, plen, rdlen))
	return NULL;
      if (type == T_OPT)
	{
	  if (len)
	    *len = ansp - start;

	  if (p)
	    *p = save;
	  
	  if (is_last)
	    *is_last = (i == arcount-1);

	  ret = start;
	}
      else if (is_sign && 
	       i == arcount - 1 && 
	       class == C_ANY && 
	       type == T_TSIG)
	*is_sign = 1;
    }
  
  return ret;
}
 

/* replace == 2 ->delete existing option only. */
size_t add_pseudoheader(struct dns_header *header, size_t plen, unsigned char *limit, 
			unsigned short udp_sz, int optno, unsigned char *opt, size_t optlen, int set_do, int replace)
{ 
  unsigned char *lenp, *datap, *p, *udp_len, *buff = NULL;
  int rdlen = 0, is_sign, is_last;
  unsigned short flags = set_do ? 0x8000 : 0, rcode = 0;

  p = find_pseudoheader(header, plen, NULL, &udp_len, &is_sign, &is_last);
  
  if (is_sign)
    return plen;

  if (p)
    {
      /* Existing header */
      int i;
      unsigned short code, len;

      p = udp_len;
      GETSHORT(udp_sz, p);
      GETSHORT(rcode, p);
      GETSHORT(flags, p);

      if (set_do)
	{
	  p -= 2;
	  flags |= 0x8000;
	  PUTSHORT(flags, p);
	}

      lenp = p;
      GETSHORT(rdlen, p);
      if (!CHECK_LEN(header, p, plen, rdlen))
	return plen; /* bad packet */
      datap = p;

       /* no option to add */
      if (optno == 0)
	return plen;
      	  
      /* check if option already there */
      for (i = 0; i + 4 < rdlen;)
	{
	  GETSHORT(code, p);
	  GETSHORT(len, p);
	  
	  /* malformed option, delete the whole OPT RR and start again. */
	  if (i + 4 + len > rdlen)
	    {
	      rdlen = 0;
	      is_last = 0;
	      break;
	    }
	  
	  if (code == optno)
	    {
	      if (replace == 0)
		return plen;

	      /* delete option if we're to replace it. */
	      p -= 4;
	      rdlen -= len + 4;
	      memmove(p, p+len+4, rdlen - i);
	      PUTSHORT(rdlen, lenp);
	      lenp -= 2;
	    }
	  else
	    {
	      p += len;
	      i += len + 4;
	    }
	}

      /* If we're going to extend the RR, it has to be the last RR in the packet */
      if (!is_last)
	{
	  /* First, take a copy of the options. */
	  if (rdlen != 0 && (buff = whine_malloc(rdlen)))
	    memcpy(buff, datap, rdlen);	      
	  
	  /* now, delete OPT RR */
	  rrfilter(header, &plen, RRFILTER_EDNS0);
	  
	  /* Now, force addition of a new one */
	  p = NULL;	  
	}
    }
  
  if (!p)
    {
      /* We are (re)adding the pseudoheader */
      if (!(p = skip_questions(header, plen)) ||
	  !(p = skip_section(p, 
			     ntohs(header->ancount) + ntohs(header->nscount) + ntohs(header->arcount), 
			     header, plen)) ||
	  p + 11 > limit)
	{
	  free(buff);
	  return plen; /* bad packet */
	}
      
      *p++ = 0; /* empty name */
      PUTSHORT(T_OPT, p);
      PUTSHORT(udp_sz, p); /* max packet length, 512 if not given in EDNS0 header */
      PUTSHORT(rcode, p);    /* extended RCODE and version */
      PUTSHORT(flags, p); /* DO flag */
      lenp = p;
      PUTSHORT(rdlen, p);    /* RDLEN */
      datap = p;
      /* Copy back any options */
      if (buff)
	{
          if (p + rdlen > limit)
          {
            free(buff);
            return plen; /* Too big */
          }
	  memcpy(p, buff, rdlen);
	  free(buff);
	  p += rdlen;
	}
      
      /* Only bump arcount if RR is going to fit */ 
      if (((ssize_t)optlen) <= (limit - (p + 4)))
	header->arcount = htons(ntohs(header->arcount) + 1);
    }
  
  if (((ssize_t)optlen) > (limit - (p + 4)))
    return plen; /* Too big */
  
  /* Add new option */
  if (optno != 0 && replace != 2)
    {
      if (p + 4 > limit)
       return plen; /* Too big */
      PUTSHORT(optno, p);
      PUTSHORT(optlen, p);
      if (p + optlen > limit)
       return plen; /* Too big */
      memcpy(p, opt, optlen);
      p += optlen;  
      PUTSHORT(p - datap, lenp);
    }
  return p - (unsigned char *)header;
}

size_t add_do_bit(struct dns_header *header, size_t plen, unsigned char *limit)
{
  return add_pseudoheader(header, plen, (unsigned char *)limit, PACKETSZ, 0, NULL, 0, 1, 0);
}

static unsigned char char64(unsigned char c)
{
  return "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[c & 0x3f];
}

static void encoder(unsigned char *in, char *out)
{
  out[0] = char64(in[0]>>2);
  out[1] = char64((in[0]<<4) | (in[1]>>4));
  out[2] = char64((in[1]<<2) | (in[2]>>6));
  out[3] = char64(in[2]);
}

/* OPT_ADD_MAC = MAC is added (if available)
   OPT_ADD_MAC + OPT_STRIP_MAC = MAC is replaced, if not available, it is only removed
   OPT_STRIP_MAC = MAC is removed */
static size_t add_dns_client(struct dns_header *header, size_t plen, unsigned char *limit,
			     union mysockaddr *l3, time_t now, int *cacheablep)
{
  int replace = 0, maclen = 0;
  unsigned char mac[DHCP_CHADDR_MAX];
  char encode[18]; /* handle 6 byte MACs ONLY */

  if ((option_bool(OPT_MAC_B64) || option_bool(OPT_MAC_HEX)) && (maclen = find_mac(l3, mac, 1, now)) == 6)
    {
      if (option_bool(OPT_STRIP_MAC))
	 replace = 1;
       *cacheablep = 0;
    
       if (option_bool(OPT_MAC_HEX))
	 print_mac(encode, mac, maclen);
       else
	 {
	   encoder(mac, encode);
	   encoder(mac+3, encode+4);
	   encode[8] = 0;
	 }
    }
  else if (option_bool(OPT_STRIP_MAC))
    replace = 2;

  if (replace != 0 || maclen == 6)
    plen = add_pseudoheader(header, plen, limit, PACKETSZ, EDNS0_OPTION_NOMDEVICEID, (unsigned char *)encode, strlen(encode), 0, replace);

  return plen;
}


/* OPT_ADD_MAC = MAC is added (if available)
   OPT_ADD_MAC + OPT_STRIP_MAC = MAC is replaced, if not available, it is only removed
   OPT_STRIP_MAC = MAC is removed */
static size_t add_mac(struct dns_header *header, size_t plen, unsigned char *limit,
		      union mysockaddr *l3, time_t now, int *cacheablep)
{
  int maclen = 0, replace = 0;
  unsigned char mac[DHCP_CHADDR_MAX];
    
  if (option_bool(OPT_ADD_MAC) && (maclen = find_mac(l3, mac, 1, now)) != 0)
    {
      *cacheablep = 0;
      if (option_bool(OPT_STRIP_MAC))
	replace = 1;
    }
  else if (option_bool(OPT_STRIP_MAC))
    replace = 2;
  
  if (replace != 0 || maclen != 0)
    plen = add_pseudoheader(header, plen, limit, PACKETSZ, EDNS0_OPTION_MAC, mac, maclen, 0, replace);

  return plen; 
}

struct subnet_opt {
  u16 family;
  u8 source_netmask, scope_netmask; 
  u8 addr[IN6ADDRSZ];
};

static void *get_addrp(union mysockaddr *addr, const short family) 
{
  if (family == AF_INET6)
    return &addr->in6.sin6_addr;

  return &addr->in.sin_addr;
}

static size_t calc_subnet_opt(struct subnet_opt *opt, union mysockaddr *source, int *cacheablep)
{
  /* http://tools.ietf.org/html/draft-vandergaast-edns-client-subnet-02 */
  
  int len;
  void *addrp = NULL;
  int sa_family = source->sa.sa_family;
  int cacheable = 0;
  
  opt->source_netmask = 0;
  opt->scope_netmask = 0;
    
  if (source->sa.sa_family == AF_INET6 && daemon->add_subnet6)
    {
      opt->source_netmask = daemon->add_subnet6->mask;
      if (daemon->add_subnet6->addr_used) 
	{
	  sa_family = daemon->add_subnet6->addr.sa.sa_family;
	  addrp = get_addrp(&daemon->add_subnet6->addr, sa_family);
	  cacheable = 1;
	} 
      else 
	addrp = &source->in6.sin6_addr;
    }

  if (source->sa.sa_family == AF_INET && daemon->add_subnet4)
    {
      opt->source_netmask = daemon->add_subnet4->mask;
      if (daemon->add_subnet4->addr_used)
	{
	  sa_family = daemon->add_subnet4->addr.sa.sa_family;
	  addrp = get_addrp(&daemon->add_subnet4->addr, sa_family);
	  cacheable = 1; /* Address is constant */
	} 
	else 
	  addrp = &source->in.sin_addr;
    }
  
  opt->family = htons(sa_family == AF_INET6 ? 2 : 1);
  
  if (addrp && opt->source_netmask != 0)
    {
      len = ((opt->source_netmask - 1) >> 3) + 1;
      memcpy(opt->addr, addrp, len);
      if (opt->source_netmask & 7)
	opt->addr[len-1] &= 0xff << (8 - (opt->source_netmask & 7));
    }
  else
    {
      cacheable = 1; /* No address ever supplied. */
      len = 0;
    }

  if (cacheablep)
    *cacheablep = cacheable;
  
  return len + 4;
}
 
/* OPT_CLIENT_SUBNET = client subnet is added
   OPT_CLIENT_SUBNET + OPT_STRIP_ECS = client subnet is replaced
   OPT_STRIP_ECS = client subnet is removed */
static size_t add_source_addr(struct dns_header *header, size_t plen, unsigned char *limit,
			      union mysockaddr *source, int *cacheable)
{
  /* http://tools.ietf.org/html/draft-vandergaast-edns-client-subnet-02 */
  
  int replace = 0, len = 0;
  struct subnet_opt opt;
  
  if (option_bool(OPT_CLIENT_SUBNET))
    {
      if (option_bool(OPT_STRIP_ECS))
	replace = 1;
      len = calc_subnet_opt(&opt, source, cacheable);
    }
  else if (option_bool(OPT_STRIP_ECS))
    replace = 2;
  else
    return plen;

  return add_pseudoheader(header, plen, (unsigned char *)limit, PACKETSZ, EDNS0_OPTION_CLIENT_SUBNET, (unsigned char *)&opt, len, 0, replace);
}

int check_source(struct dns_header *header, size_t plen, unsigned char *pseudoheader, union mysockaddr *peer)
{
  /* Section 9.2, Check that subnet option in reply matches. */
  
  int len, calc_len;
  struct subnet_opt opt;
  unsigned char *p;
  int code, i, rdlen;
  
  calc_len = calc_subnet_opt(&opt, peer, NULL);
   
  if (!(p = skip_name(pseudoheader, header, plen, 10)))
    return 1;
  
  p += 8; /* skip UDP length and RCODE */
  
  GETSHORT(rdlen, p);
  if (!CHECK_LEN(header, p, plen, rdlen))
    return 1; /* bad packet */
  
  /* check if option there */
   for (i = 0; i + 4 < rdlen; i += len + 4)
     {
       GETSHORT(code, p);
       GETSHORT(len, p);
       if (code == EDNS0_OPTION_CLIENT_SUBNET)
	 {
	   /* make sure this doesn't mismatch. */
	   opt.scope_netmask = p[3];
	   if (len != calc_len || memcmp(p, &opt, len) != 0)
	     return 0;
	 }
       p += len;
     }
   
   return 1;
}

/* See https://docs.umbrella.com/umbrella-api/docs/identifying-dns-traffic for
 * detailed information on packet formating.
 */
#define UMBRELLA_VERSION    1
#define UMBRELLA_TYPESZ     2

#define UMBRELLA_ASSET      0x0004
#define UMBRELLA_ASSETSZ    sizeof(daemon->umbrella_asset)
#define UMBRELLA_ORG        0x0008
#define UMBRELLA_ORGSZ      sizeof(daemon->umbrella_org)
#define UMBRELLA_IPV4       0x0010
#define UMBRELLA_IPV6       0x0020
#define UMBRELLA_DEVICE     0x0040
#define UMBRELLA_DEVICESZ   sizeof(daemon->umbrella_device)

struct umbrella_opt {
  u8 magic[4];
  u8 version;
  u8 flags;
  /* We have 4 possible fields since we'll never send both IPv4 and
   * IPv6, so using the larger of the two to calculate max buffer size.
   * Each field also has a type header.  So the following accounts for
   * the type headers and each field size to get a max buffer size.
   */
  u8 fields[4 * UMBRELLA_TYPESZ + UMBRELLA_ORGSZ + IN6ADDRSZ + UMBRELLA_DEVICESZ + UMBRELLA_ASSETSZ];
};

static size_t add_umbrella_opt(struct dns_header *header, size_t plen, unsigned char *limit, union mysockaddr *source, int *cacheable)
{
  *cacheable = 0;

  struct umbrella_opt opt = {{"ODNS"}, UMBRELLA_VERSION, 0, {}};
  u8 *u = &opt.fields[0];
  int family = source->sa.sa_family;
  int size = family == AF_INET ? INADDRSZ : IN6ADDRSZ;

  if (daemon->umbrella_org)
    {
      PUTSHORT(UMBRELLA_ORG, u);
      PUTLONG(daemon->umbrella_org, u);
    }
  
  PUTSHORT(family == AF_INET ? UMBRELLA_IPV4 : UMBRELLA_IPV6, u);
  memcpy(u, get_addrp(source, family), size);
  u += size;
  
  if (option_bool(OPT_UMBRELLA_DEVID))
    {
      PUTSHORT(UMBRELLA_DEVICE, u);
      memcpy(u, (char *)&daemon->umbrella_device, UMBRELLA_DEVICESZ);
      u += UMBRELLA_DEVICESZ;
    }

  if (daemon->umbrella_asset)
    {
      PUTSHORT(UMBRELLA_ASSET, u);
      PUTLONG(daemon->umbrella_asset, u);
    }
  
  return add_pseudoheader(header, plen, (unsigned char *)limit, PACKETSZ, EDNS0_OPTION_UMBRELLA, (unsigned char *)&opt, u - (u8 *)&opt, 0, 1);
}

/* Set *check_subnet if we add a client subnet option, which needs to checked 
   in the reply. Set *cacheable to zero if we add an option which the answer
   may depend on. */
size_t add_edns0_config(struct dns_header *header, size_t plen, unsigned char *limit, 
			union mysockaddr *source, time_t now, int *cacheable)    
{
/* CRADLEPOINT */
  unsigned char *edns_options = daemon->edns_options;
  size_t edns_options_len = daemon->edns_options_len;
  int i, len, code;
/* CRADLEPOINT */

  *cacheable = 1;
  
  plen  = add_mac(header, plen, limit, source, now, cacheable);
  plen = add_dns_client(header, plen, limit, source, now, cacheable);
  
  if (daemon->dns_client_id)
    plen = add_pseudoheader(header, plen, limit, PACKETSZ, EDNS0_OPTION_NOMCPEID, 
			    (unsigned char *)daemon->dns_client_id, strlen(daemon->dns_client_id), 0, 1);

  if (option_bool(OPT_UMBRELLA))
    plen = add_umbrella_opt(header, plen, limit, source, cacheable);
  
  plen = add_source_addr(header, plen, limit, source, cacheable);

  if (option_bool(OPT_GATEWAY_LAN))
    plen = add_gateway_lan(header, plen, limit, source, cacheable);

/* CRADLEPOINT */
  if (daemon->ssid_device_id_map.map) {
    opendns_pop_tag_from_query((unsigned char *) header, &plen,
	&edns_options, &edns_options_len);
  }

  if (edns_options) {
    for (i = edns_options_len; i > 0; i -= len + 4)
    {
      GETSHORT(code, edns_options);
      GETSHORT(len, edns_options);
      plen = add_pseudoheader(header, plen, limit, PACKETSZ, code,
            (unsigned char *)edns_options, len, 0, 1);
      edns_options += len;
      
      /* my_syslog(LOG_DEBUG, _("EDNS options added")); */
    }
  }
/* CRADLEPOINT */

  return plen;
}

int process_appid_catid(struct dns_header *dns_buffer, size_t dns_buffer_length)
{
  struct dns_header *header = dns_buffer;
  unsigned char *ansp;

  /* Skip DNS header and all questions records
  if packet is malformed, return as-is. */
  if (!(ansp = skip_questions(dns_buffer, dns_buffer_length)))
    return dns_buffer_length;

  /* Skip all answers records and auth records
  if packet is malformed, return as-is. */
  if (!(ansp = skip_section(ansp, ntohs(dns_buffer->ancount) + ntohs(dns_buffer->nscount),
                            dns_buffer, dns_buffer_length)))
    return dns_buffer_length;

  /* Now processing Additional records */
  if (!(ansp = process_ar(ansp, ntohs(header->arcount), dns_buffer, dns_buffer_length)))
    return dns_buffer_length;
  return dns_buffer_length;
}

unsigned char *process_ar(unsigned char *ansp, int arcount, struct dns_header *header, size_t plen)
{
  unsigned short rdlen, type, index, opcode, opdlen, oplen;

  for (index = 0; index < arcount; index++)
  {
    if (!(ansp = skip_name(ansp, header, plen, 10)))
      return NULL;
    GETSHORT(type, ansp);
    ansp += 6; /* To skip type, UDP payload size, extended RCODE and flags. Ref: RFC6891 */
    GETSHORT(rdlen, ansp);
    if (type == T_OPT)
    {
      while (rdlen > 0)
      {
        GETSHORT(opcode, ansp);
        GETSHORT(opdlen, ansp);
        /* 2 byte option code + 2 byte option length + option data */
        oplen = 2 + 2 + opdlen;
        if (opcode == EDNS0_OPTION_APPID)
        {
          insert_appid_catid_to_ipset(ansp, opdlen);
          memmove(ansp, ansp + oplen, plen - oplen);
          header->arcount = htons(arcount-1);
        }
        else if (opcode == EDNS0_OPTION_EDE) // Handle EDE (option 15)
        {
          // Move the pointer past the EDE option data
          ansp += opdlen - 1;
        }
        else
        {
          ansp += oplen;
        }
        rdlen -= oplen;
      }
    }
    else
    {
      ansp += rdlen - 8;
    }
  }
  return ansp;
}

static void insert_appid_catid_to_ipset(unsigned char *ansp, size_t plen)
{
  unsigned char *end = ansp + plen;
  char *set_ipv4, *set_ipv6;
  ansp++;
  while (ansp < end)
  {
    int entry_len = strnlen((char *)ansp, plen) + 1;
    if (ansp + entry_len > end)
    {
      // Malformed data
      return;
    }

    if ((set_ipv4 = whine_malloc(5 + entry_len)))
    {
      memcpy(set_ipv4, "ip4-", 4);
      strncpy(set_ipv4 + 4, (char *)ansp, entry_len);
      add_appid_catid_to_ipset(&daemon->ipsets, daemon->namebuff, set_ipv4);
      free(set_ipv4);
    }

    if ((set_ipv6 = whine_malloc(5 + entry_len)))
    {
      memcpy(set_ipv6, "ip6-", 4);
      strncpy(set_ipv6 + 4, (char *)ansp, entry_len);
      add_appid_catid_to_ipset(&daemon->ipsets, daemon->namebuff, set_ipv6);
      free(set_ipv6);
    }

    ansp += entry_len;
  }
}

static void add_appid_catid_to_ipset(struct ipsets **ipset, char *domain, char *set)
{
  struct ipsets *ipsetptr;
  if (!(*ipset))
  {
    if ((*ipset = whine_malloc(sizeof(struct ipsets))))
    {
      memset(*ipset, 0, sizeof(struct ipsets));
      ipsetptr = *ipset;
      if ((ipsetptr->domain = whine_malloc(strlen(domain) + 1)))
      {
        strcpy(ipsetptr->domain, domain);
      }
    }
  }

  ipsetptr = search_domain_in_ipset(*ipset, domain);

  if (!(ipsetptr))
    ipsetptr = add_domain_to_ipset(*ipset, domain);
  if (ipsetptr)
    add_set_to_ipset(ipsetptr, set);
}

static struct ipsets *search_domain_in_ipset(struct ipsets *ipset, char *domain)
{
  while (ipset)
  {
    if (strcmp(ipset->domain, domain) == 0)
    {
      return ipset;
    }
    ipset = ipset->next;
  }
  return NULL;
}

static struct ipsets *add_domain_to_ipset(struct ipsets *ipset, char *domain)
{
  while (ipset)
  {
    if (ipset->next == NULL)
      break;
    ipset = ipset->next;
  }

  if ((ipset->next = whine_malloc(sizeof(struct ipsets))))
  {
    memset(ipset->next, 0, sizeof(struct ipsets));
    if ((ipset->next->domain = whine_malloc(strlen(domain) + 1)))
    {
      strcpy(ipset->next->domain, domain);
      return ipset->next;
    }
  }
  return NULL;
}

static void add_set_to_ipset(struct ipsets *ipset, char *set)
{
  int count = 0;
  if (!(ipset->sets))
  {
    if ((ipset->sets = realloc(ipset->sets, 2 * sizeof(char *))) == NULL)
    {
      my_syslog(LOG_ERR, _("failed to allocate %d bytes"), (int)(2 * sizeof(char *)));
      return;
    }
    if ((ipset->sets[0] = whine_malloc(strlen(set) + 1)))
    {
      strcpy(ipset->sets[0], set);
      ipset->sets[1] = NULL;
      return;
    }
  }

  while (ipset->sets[count])
  {
    if (strcmp(ipset->sets[count], set) == 0)
      return;
    else
      count++;
  }
  if ((ipset->sets = realloc(ipset->sets, sizeof(char *) * (count + 2))) == NULL)
  {
    my_syslog(LOG_ERR, _("failed to allocate %d bytes"), (int)((count + 2) * sizeof(char *)));
    return;
  }
  if ((ipset->sets[count] = whine_malloc(strlen(set) + 1)))
  {
    strcpy(ipset->sets[count], set);
    ipset->sets[count + 1] = NULL;
  }
  return;
}

static size_t add_gateway_lan(struct dns_header *header, size_t plen, unsigned char *limit,
                             union mysockaddr *source, int *cacheable)
{
  *cacheable = 0;
  struct dhcp_context *context = NULL;
  int if_index = 0;

  /* Try to determine interface using socket information */
  /* Create a socket to determine the best route to the client */
  char ifname[IF_NAMESIZE] = {0};

  /* Find interface index using socket-based method */
  if (source->sa.sa_family == AF_INET)
  {
    struct irec *iface;
    int temp_fd = -1;

    /* Method 1: Use socket to determine which interface would be used to reach the client */
    if ((temp_fd = socket(AF_INET, SOCK_DGRAM, 0)) != -1)
    {
      socklen_t addr_len = sizeof(union mysockaddr);

      /* Connect to the client's address to determine routing */
      if (connect(temp_fd, &source->sa, sizeof(struct sockaddr_in)) == 0)
      {
        union mysockaddr local_addr;
        if (getsockname(temp_fd, &local_addr.sa, &addr_len) == 0)
        {
          /* Find the interface that matches this local address */
          for (iface = daemon->interfaces; iface; iface = iface->next)
          {
            if (iface->addr.sa.sa_family == AF_INET &&
                iface->addr.in.sin_addr.s_addr == local_addr.in.sin_addr.s_addr)
            {
              if_index = iface->index;
              break;
            }
          }
        }
      }
      close(temp_fd);
    }

    /* Method 2: If socket method failed, try to find the first non-loopback interface with DHCP enabled */
    if (!if_index)
    {
      for (iface = daemon->interfaces; iface; iface = iface->next)
      {
        if (iface->addr.sa.sa_family == AF_INET &&
            iface->dhcp4_ok &&
            !IN_LOOPBACK(ntohl(iface->addr.in.sin_addr.s_addr)))
        {
          if_index = iface->index;
          break;
        }
      }
    }

    /* Method 3: If no DHCP-enabled interface found, use first non-loopback IPv4 interface */
    if (!if_index)
    {
      for (iface = daemon->interfaces; iface; iface = iface->next)
      {
        if (iface->addr.sa.sa_family == AF_INET &&
            !IN_LOOPBACK(ntohl(iface->addr.in.sin_addr.s_addr)))
        {
          if_index = iface->index;
          break;
        }
      }
    }

    /* Method 4: As last resort, use any IPv4 interface */
    if (!if_index)
    {
      for (iface = daemon->interfaces; iface; iface = iface->next)
      {
        if (iface->addr.sa.sa_family == AF_INET)
        {
          if_index = iface->index;
          break;
        }
      }
    }
  }

  if (if_index && if_indextoname(if_index, ifname))
  {
    /* Find DHCP context for this interface */
    for (context = daemon->dhcp; context; context = context->next)
    {
      /* Check if this context is for the same interface */
      if (context->netid.net &&
          strcmp(context->netid.net, ifname) == 0)
      {
        struct in_addr gateway_ip, netmask;
        char gateway_str[64] = {0}; /* Buffer for formatted string */

        /* Use router/gateway address if available, otherwise use start address */
        if (context->router.s_addr != 0)
          gateway_ip = context->router;
        else
          gateway_ip = context->start;

        /* Use netmask from DHCP context */
        netmask = context->netmask;

        /* Format the gateway IP and netmask as a human-readable string */
        char ip_str[INET_ADDRSTRLEN];
        char mask_str[INET_ADDRSTRLEN];

        /* Check if conversion to string was successful */
        if (!inet_ntop(AF_INET, &gateway_ip, ip_str, sizeof(ip_str)) ||
            !inet_ntop(AF_INET, &netmask, mask_str, sizeof(mask_str))) {
          my_syslog(LOG_ERR, _("Failed to convert IP/netmask to string for gateway LAN option"));
          return plen;
        }

        /* Use snprintf safely and check if the buffer was large enough */
        int printed = snprintf(gateway_str, sizeof(gateway_str), "%s, %s", ip_str, mask_str);
        if (printed < 0 || (size_t)printed >= sizeof(gateway_str)) {
          my_syslog(LOG_ERR, _("Gateway LAN IP string too long for buffer"));
          return plen;
        }

        /* Add to EDNS payload with custom option code and check return value */
        size_t new_plen = add_pseudoheader(header, plen, limit, PACKETSZ,
                                EDNS0_OPTION_GATEWAY_LAN_IP, (unsigned char *)gateway_str,
                                strlen(gateway_str), 0, 1);

        /* Check if add_pseudoheader was successful */
        if (new_plen == plen) {
          my_syslog(LOG_ERR, _("Failed to add Gateway LAN IP EDNS0 option"));
          return plen;
        }

        plen = new_plen;

        /* Only use the first matching context to avoid multiple options */
        break;
      }
    }
  }
  else if (if_index == 0)
  {
    /* Log that we couldn't find any suitable interface using socket-based detection */
    my_syslog(LOG_DEBUG, _("Gateway LAN IP: Could not determine interface using socket-based method"));
  }
  else
  {
    /* Log that if_indextoname failed */
    my_syslog(LOG_DEBUG, _("Gateway LAN IP: Failed to get interface name for index %d"), if_index);
  }
  return plen;
}
