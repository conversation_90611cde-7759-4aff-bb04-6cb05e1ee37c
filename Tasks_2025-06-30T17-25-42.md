[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:Analyze current ipset functionality and requirements DESCRIPTION:Understand how the current ipset system works: 1) ipsets are configured with domain patterns via --ipset option, 2) domain_find_sets() matches query names against configured patterns, 3) extract_addresses() adds IPs to matching ipsets. The enhancement needed is to also check for FQDN-based ipset names that match the queried domain name from DNS responses.
-[x] NAME:Design FQDN-based ipset matching algorithm DESCRIPTION:Design algorithm to: 1) Extract the queried domain name from DNS response, 2) Generate FQDN-based ipset names (e.g., 'fqdn_www_reddit_com' from 'www.reddit.com'), 3) Check if any configured ipsets match the generated FQDN pattern, 4) Update matching ipsets with resolved IP addresses. This should work alongside existing domain-pattern based ipsets.
-[x] NAME:Implement FQDN-to-ipset-name conversion function DESCRIPTION:Create a function to convert domain names to FQDN-based ipset names by replacing dots with underscores and adding 'fqdn_' prefix. Handle edge cases like empty domains, very long names, and special characters. Function should be reusable for both IPv4 and IPv6 ipset name generation.
-[x] NAME:Implement FQDN-based ipset matching in extract_addresses DESCRIPTION:Modify the extract_addresses function in rfc1035.c to: 1) Generate FQDN-based ipset names from the queried domain, 2) Search through configured ipsets to find matches, 3) Add resolved IP addresses to matching FQDN-based ipsets. This should work in addition to the existing domain-pattern matching.
-[x] NAME:Add support for IPv4 and IPv6 FQDN-based ipsets DESCRIPTION:Ensure the implementation supports both IPv4 ('ip4-fqdn_*') and IPv6 ('ip6-fqdn_*') ipset naming conventions. The system should automatically determine the appropriate ipset type based on the IP address type being added.
-[x] NAME:Test the implementation with sample DNS responses DESCRIPTION:Create test scenarios to verify: 1) FQDN-based ipsets are correctly identified and updated, 2) Both IPv4 and IPv6 addresses are handled properly, 3) The enhancement doesn't break existing domain-pattern based ipset functionality, 4) Edge cases like malformed domains are handled gracefully.