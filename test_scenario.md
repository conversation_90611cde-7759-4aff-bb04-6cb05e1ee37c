# FQDN-based ipset Enhancement Test Scenarios

## Overview
This document describes test scenarios for the enhanced dnsmasq functionality that updates ipsets based on FQDN matching from DNS query responses.

## Test Scenario 1: Basic FQDN-based ipset matching

### Setup
Configure dnsmasq with FQDN-based ipsets:
```bash
# Create ipsets for specific domains
ipset create ip4-fqdn_www_reddit_com hash:ip family inet
ipset create ip6-fqdn_www_reddit_com hash:ip family inet6
ipset create ip4-fqdn_example_com hash:ip family inet
```

Configure dnsmasq:
```bash
dnsmasq --ipset=/#/ip4-fqdn_www_reddit_com,ip6-fqdn_www_reddit_com,ip4-fqdn_example_com
```

### Test Case 1.1: IPv4 DNS Response for www.reddit.com
**DNS Query**: A record for www.reddit.com
**Expected DNS Response**:
```
Query: www.reddit.com type A
Answer: www.reddit.com -> *************
```

**Expected Behavior**:
1. dnsmasq extracts query name: "www.reddit.com"
2. Generates FQDN ipset name: "fqdn_www_reddit_com"
3. Searches configured ipsets for "ip4-fqdn_www_reddit_com"
4. Finds match and adds ************* to ip4-fqdn_www_reddit_com ipset
5. Logs: "query[A] www.reddit.com from ... is *************, added to ipset ip4-fqdn_www_reddit_com"

**Verification**:
```bash
ipset list ip4-fqdn_www_reddit_com
# Should show ************* in the set
```

### Test Case 1.2: IPv6 DNS Response for www.reddit.com
**DNS Query**: AAAA record for www.reddit.com
**Expected DNS Response**:
```
Query: www.reddit.com type AAAA
Answer: www.reddit.com -> 2001:db8::1
```

**Expected Behavior**:
1. dnsmasq extracts query name: "www.reddit.com"
2. Generates FQDN ipset name: "fqdn_www_reddit_com"
3. Searches configured ipsets for "ip6-fqdn_www_reddit_com"
4. Finds match and adds 2001:db8::1 to ip6-fqdn_www_reddit_com ipset

## Test Scenario 2: No matching FQDN-based ipset

### Test Case 2.1: Query for non-configured domain
**DNS Query**: A record for google.com
**Expected Behavior**:
1. dnsmasq extracts query name: "google.com"
2. Generates FQDN ipset name: "fqdn_google_com"
3. Searches configured ipsets for "ip4-fqdn_google_com"
4. No match found, no ipset update occurs
5. Normal DNS response processing continues

## Test Scenario 3: Coexistence with existing domain-pattern ipsets

### Setup
```bash
# Traditional domain-pattern ipset
dnsmasq --ipset=/reddit.com/reddit_ips --ipset=/#/ip4-fqdn_www_reddit_com
```

### Test Case 3.1: Both pattern and FQDN ipsets match
**DNS Query**: A record for www.reddit.com
**Expected Behavior**:
1. Traditional domain matching: www.reddit.com matches /reddit.com/ pattern
2. IP added to reddit_ips ipset (existing functionality)
3. FQDN matching: generates "fqdn_www_reddit_com"
4. IP also added to ip4-fqdn_www_reddit_com ipset (new functionality)
5. Both ipsets are updated with the same IP

## Test Scenario 4: Edge Cases

### Test Case 4.1: Empty or malformed domain
**DNS Query**: Malformed query
**Expected Behavior**:
1. domain_to_fqdn_ipset_name() returns NULL for invalid input
2. No FQDN-based ipset processing occurs
3. Normal error handling continues

### Test Case 4.2: Very long domain name
**DNS Query**: A record for very.long.domain.name.with.many.subdomains.example.com
**Expected Behavior**:
1. If domain length exceeds limits, domain_to_fqdn_ipset_name() returns NULL
2. No FQDN-based ipset processing occurs
3. Normal DNS processing continues

## Test Scenario 5: nftset Support

### Setup
```bash
# Create nftables sets
nft add table ip test
nft add set ip test fqdn_www_reddit_com { type ipv4_addr\; }
```

Configure dnsmasq:
```bash
dnsmasq --nftset=/#/ip#test#fqdn_www_reddit_com
```

### Test Case 5.1: nftset FQDN matching
**DNS Query**: A record for www.reddit.com
**Expected Behavior**:
1. FQDN matching generates "fqdn_www_reddit_com"
2. Searches nftsets for matching pattern
3. Finds "ip#test#fqdn_www_reddit_com" contains "fqdn_www_reddit_com"
4. Adds IP to nftables set

## Implementation Verification

The enhancement has been implemented in `src/rfc1035.c` with the following key functions:

1. `domain_to_fqdn_ipset_name()`: Converts domain to FQDN ipset name format
2. `find_fqdn_ipsets()`: Searches configured ipsets for FQDN matches
3. Enhanced `extract_addresses()`: Adds FQDN-based ipset processing

The implementation:
- ✅ Supports both IPv4 and IPv6 ipsets
- ✅ Works alongside existing domain-pattern matching
- ✅ Handles edge cases gracefully
- ✅ Supports both ipset and nftset
- ✅ Maintains backward compatibility
