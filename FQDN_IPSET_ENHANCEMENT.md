# FQDN-based ipset Enhancement for dnsmasq

## Overview

This enhancement adds support for FQDN-based ipset updates in dnsmasq. When processing DNS responses, dnsmasq will now check if any configured ipsets match the queried domain name using FQDN-based naming conventions and update those ipsets with the resolved IP addresses.

## Example Use Case

For a DNS query to `www.reddit.com` that resolves to `*************`, if there's an ipset named `ip4-fqdn_www_reddit_com` configured, dnsmasq will automatically add the IP address to that ipset.

## Implementation Details

### Files Modified
- `src/rfc1035.c`: Added FQDN-based ipset functionality

### New Functions Added

#### `domain_to_fqdn_ipset_name(const char *domain)`
- Converts domain names to FQDN-based ipset name format
- Example: `"www.reddit.com"` → `"fqdn_www_reddit_com"`
- Replaces dots with underscores and adds "fqdn_" prefix
- Returns allocated string that must be freed by caller
- Returns NULL for invalid input or memory allocation failures

#### `find_fqdn_ipsets(struct ipsets *ipsets_list, const char *domain)`
- Searches configured ipsets for FQDN-based matches
- Looks for ipsets with names like `"ip4-fqdn_..."` or `"ip6-fqdn_..."`
- Returns pointer to matching ipset structure or NULL if not found

### Enhanced Functionality in `extract_addresses()`

The main DNS response processing function now includes:

1. **FQDN-based ipset matching**: After processing traditional domain-pattern ipsets, the function now:
   - Generates FQDN-based ipset names from the queried domain
   - Searches configured ipsets for matches
   - Adds resolved IP addresses to matching FQDN-based ipsets

2. **IPv4/IPv6 support**: 
   - IPv4 addresses are added to ipsets with names starting with `"ip4-fqdn_"`
   - IPv6 addresses are added to ipsets with names starting with `"ip6-fqdn_"`

3. **nftset support**: Similar functionality for nftables sets with FQDN patterns

## Configuration

### ipset Configuration
```bash
# Create FQDN-based ipsets
ipset create ip4-fqdn_www_reddit_com hash:ip family inet
ipset create ip6-fqdn_www_reddit_com hash:ip family inet6

# Configure dnsmasq to use them
dnsmasq --ipset=/#/ip4-fqdn_www_reddit_com,ip6-fqdn_www_reddit_com
```

### nftset Configuration
```bash
# Create nftables sets
nft add table ip test
nft add set ip test fqdn_www_reddit_com { type ipv4_addr\; }

# Configure dnsmasq
dnsmasq --nftset=/#/ip#test#fqdn_www_reddit_com
```

## Behavior

### Normal Operation
1. DNS query for `www.reddit.com` is received
2. dnsmasq forwards query and receives response with IP `*************`
3. Traditional domain-pattern matching occurs (existing functionality)
4. **NEW**: FQDN-based matching occurs:
   - Generates name `"fqdn_www_reddit_com"`
   - Searches for ipset `"ip4-fqdn_www_reddit_com"`
   - If found, adds `*************` to the ipset
5. DNS response is returned to client

### Coexistence with Existing Functionality
- FQDN-based ipset updates work **in addition to** existing domain-pattern matching
- Both types of ipsets can be updated for the same DNS response
- No changes to existing configuration or behavior

### Error Handling
- Invalid domain names are handled gracefully (no ipset updates)
- Memory allocation failures are handled safely
- Very long domain names are rejected to prevent excessive memory usage

## Advantages

1. **Precise Matching**: Exact domain name matching without wildcards
2. **Predictable Naming**: Clear, consistent ipset naming convention
3. **Backward Compatible**: No changes to existing functionality
4. **Flexible**: Works with both ipset and nftset
5. **Efficient**: Minimal performance impact

## Limitations

1. **Exact Match Only**: Only matches exact domain names, not subdomains
2. **Manual ipset Creation**: ipsets must be created manually before use
3. **Naming Convention**: Requires specific naming pattern (`ip4-fqdn_*`, `ip6-fqdn_*`)

## Testing

The implementation has been tested for:
- ✅ Correct FQDN name generation
- ✅ IPv4 and IPv6 address handling
- ✅ Edge case handling (empty domains, long names)
- ✅ Compilation without errors
- ✅ Coexistence with existing functionality

See `test_scenario.md` for detailed test scenarios and expected behaviors.
